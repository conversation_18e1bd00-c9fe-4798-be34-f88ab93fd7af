/**
 * Helper function to set a cookie
 * @param {string} name - Cookie name
 * @param {any} value - Cookie value
 * @param {Object} options - Cookie options
 * @param {number} options.days - Cookie expiration in days (default: 7)
 * @param {string} options.path - Cookie path (default: '/')
 * @param {boolean} options.secure - Whether cookie should only be sent over HTTPS (default: true in production)
 * @param {boolean} options.sameSite - SameSite attribute (default: 'Lax')
 */
export function setCookieHelper(name, value, options = {}) {
  const {
    days = 7,
    path = "/",
    secure = window.location.protocol === "https:",
    sameSite = "Lax",
  } = options;
  
  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
  
  let cookieValue;
  
  if (typeof value === "string") {
    cookieValue = encodeURIComponent(value);
  } else {
    cookieValue = encodeURIComponent(JSON.stringify(value));
  }
  
  document.cookie = `${name}=${cookieValue}; expires=${expires.toUTCString()}; path=${path}${
    secure ? "; Secure" : ""
  }; SameSite=${sameSite}`;
}

/**
 * Helper function to get a cookie by name
 * @param {string} name - Cookie name
 * @returns {any} - Cookie value
 */
export function getCookieHelper(name) {
  const cookies = document.cookie.split("; ");
  const cookie = cookies.find((c) => c.startsWith(`${name}=`));
  
  if (!cookie) return null;
  
  const cookieValue = cookie.split("=")[1];
  
  try {
    // Try to parse as JSON
    return JSON.parse(decodeURIComponent(cookieValue));
  } catch {
    // If parsing fails, return as string
    return decodeURIComponent(cookieValue);
  }
}

/**
 * Helper function to remove a cookie
 * @param {string} name - Cookie name
 * @param {Object} options - Cookie options
 * @param {string} options.path - Cookie path (default: '/')
 */
export function removeCookieHelper(name, options = {}) {
  const { path = "/" } = options;
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`;
}
