import { MAIN_DOMAIN, FT_DOMAIN, IS_DEV_MODE } from "./consts";

export const isOnFastTrackDomain = () => {
  return window.location.host === FT_DOMAIN;
};

export const redirectToMainPortal = () => {
  if (IS_DEV_MODE) return;
  const currentPath = window.location.pathname;
  const currentSearch = window.location.search;
  const mainUrl = `https://${MAIN_DOMAIN}${currentPath}${currentSearch}`;
  window.location.href = mainUrl;
};

export const redirectToFastTrack = () => {
  if (IS_DEV_MODE) return;
  const currentPath = window.location.pathname;
  const currentSearch = window.location.search;
  const ftUrl = `https://${FT_DOMAIN}${currentPath}${currentSearch}`;
  window.location.href = ftUrl;
};
