import { useRef, useCallback } from "react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { FastTrackForm } from "../components/FastTrackForm/FastTrackForm";
import { useValidateRepApi } from "../hooks/useValidateRepApi";
import { useAppStorage } from "../hooks/useAppStorage";
import { PreQualifyExplainer, QuickLinksSection } from "./PreQualifyPage";
import { trackCustomEvent } from "../utils/analytics";
import {
  isOnFastTrackDomain,
  redirectToFastTrack,
  redirectToMainPortal,
} from "../utils/domainUtils";
import { IS_DEV_MODE } from "../utils/consts";

/**
 * Fast Track Page Component
 * Validates utm_rep parameter and shows fast track form if valid
 * @returns {JSX.Element}
 */
export const FastTrackPage = () => {
  const navigate = useNavigate();
  const {
    utmParams,
    fastTrackFormParams,
    fastTrackActive,
    setFastTrackActive,
  } = useAppStorage();
  const { validateRep } = useValidateRepApi();
  const formInitialized = useRef(false);
  const [isValidRep, setIsValidRep] = useState(false);

  const handleDomainRedirect = useCallback(() => {
    setIsValidRep(false);
    setFastTrackActive(false);

    if (IS_DEV_MODE) {
      navigate("/", { replace: true });
      return;
    }

    if (isOnFastTrackDomain()) {
      redirectToMainPortal();
    } else {
      navigate("/", { replace: true });
    }
  }, [setFastTrackActive, navigate]);

  useEffect(() => {
    if (formInitialized.current) return;

    const checkRepValidation = async () => {
      // Check if utm_rep parameter exists
      const utmRep = utmParams?.utm_rep;

      if (!utmRep) {
        // No utm_rep parameter, redirect to prequalify page
        handleDomainRedirect();
        return;
      }

      try {
        // Validate the rep parameter
        const validationResult = await validateRep(utmRep);

        if (!validationResult?.valid) {
          // Invalid rep, redirect to prequalify page
          handleDomainRedirect();
          return;
        } else {
          if (!formInitialized.current) {
            trackCustomEvent("fast_track", true);
            setIsValidRep(true);
            formInitialized.current = true;
            if (!fastTrackActive) setFastTrackActive(true);

            if (!isOnFastTrackDomain()) {
              redirectToFastTrack();
            }
          }
        }
      } catch (error) {
        // Error validating rep, redirect to prequalify page
        console.error("Error validating rep:", error);
        handleDomainRedirect();
      }
    };

    checkRepValidation();
  }, [
    utmParams,
    validateRep,
    navigate,
    fastTrackActive,
    setFastTrackActive,
    handleDomainRedirect,
  ]);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col lg:flex-row lg:space-x-8 py-4 lg:py-8">
        <div className="w-full lg:w-1/3 mb-6 lg:mb-0">
          <PreQualifyExplainer />
        </div>

        <div className="w-full lg:w-2/3">
          <FastTrackForm
            fastTrackParams={fastTrackFormParams}
            isValidRep={isValidRep}
          />
        </div>
      </div>
      <QuickLinksSection />
    </div>
  );
};
