import React, { useState } from "react";

const FAQItem = ({ question, answer, isOpen, toggleOpen }) => {
  return (
    <div className="border-b border-gray-200 py-5">
      <button
        className="flex w-full justify-between items-center text-left focus:outline-none group"
        onClick={toggleOpen}
      >
        <h3
          className={`text-lg font-medium ${
            isOpen ? "text-blue-700" : "text-gray-900"
          } group-hover:text-blue-700 transition-colors duration-200`}
        >
          {question}
        </h3>
        <span className="ml-6 flex-shrink-0">
          <svg
            className={`h-6 w-6 transform transition-transform duration-200 ${
              isOpen ? "rotate-180 text-blue-600" : "rotate-0 text-gray-500"
            } group-hover:text-blue-600`}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </span>
      </button>
      <div
        className={`mt-2 pr-12 overflow-hidden transition-all duration-300 ease-in-out ${
          isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
        }`}
      >
        <div className="text-base text-gray-700 whitespace-pre-line pb-2">
          {answer}
        </div>
      </div>
    </div>
  );
};

export const FAQ = () => {
  const [openIndex, setOpenIndex] = useState(0);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? -1 : index);
  };

  const faqItems = [
    {
      question: "What do I need to qualify?",
      answer:
        "Minimum one year in business.\nMinimum of $100,000 in annual revenue\nMinimum 575 Personal FICO score\nBusiness checking account\n\nYou can read more about qualification in our resources section over here.",
    },
    {
      question: "Is there any cost to apply?",
      answer: "Applications always have been and will always remain free.",
    },
    {
      question: "Which documents will I need to submit",
      answer:
        "Pinnacle only requires basic information about your business and yourself. We may ask for: Your business tax ID, your last three months of business bank statements, and the Social Security number of all business owner(s).",
    },
    {
      question: "How long is the application process",
      answer:
        "We pride ourselves on our quick and simple application process. It takes under 5 minutes to fill out our online application. You can also apply over the phone by calling (347) 694 – 8180",
    },
    {
      question: "How soon can I get my funds?",
      answer:
        "Once your application is approved, we'll deposit the funds into your account quickly – as soon as a couple hours.",
    },
    {
      question: "Will there be a hard credit pull?",
      answer:
        "No. If there are complications in the application process and a hard credit pull is necessary, we will notify you beforehand.",
    },
    {
      question: "Can I receive more funds after my first funding?",
      answer:
        "Yes! Assuming payments are made in good order, we'll be happy to offer you more future funding.",
    },
  ];

  return (
    <div className="max-w-4xl mx-auto  mt-12 py-16 bg-gray-50 bg-opacity-80 bg-repeat">
      <div className=" px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-blue-900 mb-6">
            Questions & Answers
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            For more detailed answers to your questions, take a look at our
            resources page or speak to one of our team members at{" "}
            <a
              href="tel:3476948180"
              className="text-blue-600 hover:underline font-medium"
            >
              (*************
            </a>
          </p>
        </div>

        <div className="max-w-3xl mx-auto divide-y divide-gray-200 bg-white rounded-lg shadow-sm p-6">
          {faqItems.map((faq, index) => (
            <FAQItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              isOpen={openIndex === index}
              toggleOpen={() => toggleFAQ(index)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
