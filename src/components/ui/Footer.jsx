import { Link } from "react-router-dom";
import BBB_Image from "/src/assets/bbb-review.webp";
import TrustPilot_Image from "/src/assets/trustpilot-review.webp";
import { trackClick } from "../../utils/analytics.js";

const Copyright = () => {
  const handlePrivacyPolicyClick = () => {
    trackClick("Privacy Policy", {
      destination: "https://pinnaclefundingco.com/privacy-policy/",
    });
  };

  const handleTermsClick = () => {
    trackClick("Terms & Conditions", {
      destination: "https://pinnaclefundingco.com/privacy-policy/",
    });
  };

  return (
    <div className="text-xs sm:text-sm px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
        <p>
          &copy; {new Date().getFullYear()} Pinnacle Funding. All rights
          reserved.
        </p>
        <p>
          <span className="hover:underline">
            <a
              target="_blank"
              href="https://pinnaclefundingco.com/privacy-policy/"
              onClick={handlePrivacyPolicyClick}
            >
              Privacy Policy
            </a>
          </span>{" "}
          |{" "}
          <span className="hover:underline">
            <a
              target="_blank"
              href="https://pinnaclefundingco.com/privacy-policy/"
              onClick={handleTermsClick}
            >
              Terms & Conditions{" "}
            </a>
          </span>
        </p>
      </div>
    </div>
  );
};

const TrustPilotImg = () => {
  const handleTrustpilotClick = () => {
    trackClick("Trustpilot", {
      destination: "https://www.trustpilot.com/review/pinnacleconsultingny.com",
    });
  };

  return (
    <a
      target="_blank"
      href="https://www.trustpilot.com/review/pinnacleconsultingny.com"
      onClick={handleTrustpilotClick}
    >
      <img
        src={TrustPilot_Image}
        alt="Trustpilot"
        className="h-8 sm:h-10 lg:h-12 rounded-sm"
      />
    </a>
  );
};

const BBBImg = () => {
  const handleBBBClick = () => {
    trackClick("BBB", {
      destination:
        "https://www.bbb.org/us/ny/brooklyn/profile/financial-services/pinnacle-funding-0121-87175014",
    });
  };

  return (
    <a
      target="_blank"
      href="https://www.bbb.org/us/ny/brooklyn/profile/financial-services/pinnacle-funding-0121-87175014"
      onClick={handleBBBClick}
    >
      <img
        src={BBB_Image}
        alt="Better Business Bureau"
        className="h-8 sm:h-10 lg:h-12"
      />
    </a>
  );
};

export const Footer = () => {
  return (
    <footer className="bg-[#23448F] text-[#B4C2F8] py-4">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <article className="max-w-sm grid gap-4 sm:gap-6 lg:gap-8">
          <h5>
            <Link
              onClick={() => trackClick("Footer Logo")}
              className="font-bold text-xl sm:text-2xl"
              to={"/"}
            >
              Pinnacle Funding
            </Link>
          </h5>
          <p className="text-sm font-light sm:text-base">
            We are a premier alternative lender proudly serving small and
            mid-sized businesses.
          </p>
          <div className="flex gap-3 sm:gap-4">
            <TrustPilotImg />
            <BBBImg />
          </div>
        </article>
      </div>

      <hr className="w-ful h-px max-w-7xl my-3 sm:my-4 mx-auto" />
      <div className="py-3 sm:py-4">
        <Copyright />
      </div>
    </footer>
  );
};
